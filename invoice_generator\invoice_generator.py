import sys
from PyQt5.QtWidgets import (QApplication, QWidget, QLabel, 
                            QLineEdit, QPushButton, QFileDialog, QMessageBox,
                            QGridLayout)
from PyQt5.QtCore import Qt
import pandas as pd
from docxtpl import DocxTemplate
import os
import subprocess
from datetime import datetime
import win32com.client
import pythoncom

class InvoiceGenerator(QWidget):
    # 显式声明layout属性
    layout: QGridLayout

    def __init__(self):
        super().__init__()
        self.setWindowTitle("发票批量生成器")

        # 文件选择
        self.excel_file = None
        self.template_file = os.path.join(os.path.dirname(__file__), "invoice_template.docx")

        # COM对象复用 - 添加Word应用实例变量
        self.word_app = None
        self.com_initialized = False

        # 模板预加载缓存
        self.cached_template = None
        self.cached_template_path = None

        # 创建默认输出文件夹
        self.default_output_dir = os.path.join(os.getcwd(), "generated_invoices")
        if not os.path.exists(self.default_output_dir):
            os.makedirs(self.default_output_dir)

        # 布局
        self.layout = QGridLayout()
        self.setLayout(self.layout)

        # 状态标签
        self.status_label = QLabel("")

        # GUI组件
        self.create_widgets()

    def init_word_app(self):
        """初始化Word应用程序实例"""
        if not self.com_initialized:
            pythoncom.CoInitialize()
            self.com_initialized = True

        if self.word_app is None:
            try:
                # 尝试获取已存在的Word实例
                self.word_app = win32com.client.GetObject(Class="Word.Application")
            except:
                # 创建新的Word实例
                self.word_app = win32com.client.Dispatch("Word.Application")

            self.word_app.Visible = False
            self.word_app.DisplayAlerts = False

    def cleanup_word_app(self):
        """清理Word应用程序实例"""
        if self.word_app is not None:
            try:
                self.word_app.Quit()
            except:
                pass
            finally:
                self.word_app = None

        if self.com_initialized:
            pythoncom.CoUninitialize()
            self.com_initialized = False

    def load_template(self, template_path):
        """预加载模板，避免重复加载"""
        if self.cached_template is None or self.cached_template_path != template_path:
            self.cached_template = DocxTemplate(template_path)
            self.cached_template_path = template_path
        return self.cached_template

    def get_template(self):
        """获取缓存的模板实例（可重复使用）"""
        if self.cached_template is None:
            raise ValueError("模板未加载，请先调用load_template")
        # DocxTemplate实例可以重复使用，无需创建副本
        return self.cached_template

    def closeEvent(self, event):
        """窗口关闭时清理资源"""
        self.cleanup_word_app()
        event.accept()

    def create_widgets(self):
        # Excel文件选择
        self.layout.addWidget(QLabel("Excel数据源:"), 0, 0)
        self.excel_path = QLineEdit()
        self.excel_path.setReadOnly(True)
        self.layout.addWidget(self.excel_path, 0, 1)
        self.excel_btn = QPushButton("选择文件")
        self.excel_btn.clicked.connect(self.select_excel)
        self.layout.addWidget(self.excel_btn, 0, 2)
        
        # 模板文件选择
        self.layout.addWidget(QLabel("Word模板:"), 1, 0)
        self.template_path = QLineEdit(self.template_file)
        self.template_path.setReadOnly(True)
        self.layout.addWidget(self.template_path, 1, 1)
        self.template_btn = QPushButton("选择文件")
        self.template_btn.clicked.connect(self.select_template)
        self.layout.addWidget(self.template_btn, 1, 2)
        
        # 生成设置
        self.layout.addWidget(QLabel("柜号:"), 2, 0)
        self.container_num = QLineEdit()
        self.layout.addWidget(self.container_num, 2, 1)
        
        self.layout.addWidget(QLabel("发票号:"), 3, 0)
        self.invoice_num = QLineEdit()
        self.layout.addWidget(self.invoice_num, 3, 1)
        
        # 起始行号
        self.layout.addWidget(QLabel("起始行号:"), 4, 0)
        self.start_row = QLineEdit()
        self.layout.addWidget(self.start_row, 4, 1)
        
        # 结束行号
        self.layout.addWidget(QLabel("结束行号:"), 5, 0)
        self.end_row = QLineEdit()
        self.layout.addWidget(self.end_row, 5, 1)
        
        # 提示信息
        self.layout.addWidget(QLabel("提示：如果只需要生成一条数据对应的发票，两个输入框应该填同一个行号。"), 
                            6, 0, 1, 3)
        
        # 运输编号（原输出文件夹）
        self.layout.addWidget(QLabel("运输编号:"), 8, 0)
        self.output_folder = QLineEdit()
        self.layout.addWidget(self.output_folder, 8, 1)
        
        # 生成按钮
        self.generate_btn = QPushButton("生成发票")
        self.generate_btn.clicked.connect(self.generate_invoices)
        self.layout.addWidget(self.generate_btn, 9, 1)
        
        # 状态标签
        self.status_label.setStyleSheet("color: blue;")
        self.layout.addWidget(self.status_label, 10, 0, 1, 3)
        
    def select_excel(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择Excel文件", "", 
                                                 "Excel files (*.xlsx *.xls)")
        if file_path:
            self.excel_path.setText(file_path)
            self.excel_file = file_path
            
    def select_template(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择Word模板", "", 
                                                 "Word files (*.docx)")
        if file_path:
            self.template_path.setText(file_path)
            
    def convert_to_pdf(self, word_path):
        """将Word文档转换为PDF - 使用复用的Word应用实例"""
        doc = None
        try:
            # 确保文件存在
            if not os.path.exists(word_path):
                raise FileNotFoundError(f"文件不存在: {word_path}")

            # 确保Word应用已初始化
            if self.word_app is None:
                self.init_word_app()

            # 打开文档
            doc = self.word_app.Documents.Open(os.path.abspath(word_path))
            pdf_path = word_path.replace('.docx', '.pdf')

            # 保存为PDF
            doc.SaveAs(
                FileName=os.path.abspath(pdf_path),
                FileFormat=17  # PDF格式
            )

            # 只关闭文档，不关闭Word应用
            doc.Close(SaveChanges=0)

            return pdf_path

        except Exception as e:
            # 清理文档但保留Word应用
            if doc:
                try:
                    doc.Close(SaveChanges=0)
                except:
                    pass
            raise Exception(f"转换PDF时出错: {str(e)}")

    def update_status(self, message):
        """更新状态标签文本"""
        self.status_label.setText(message)
        QApplication.processEvents()  # 确保UI更新

    def generate_invoices(self):
        self.error_log = []  # 初始化错误日志
        try:
            # 禁用生成按钮
            self.generate_btn.setEnabled(False)
            self.update_status("正在初始化Word应用...")

            # 初始化Word应用（一次性初始化，提升性能）
            self.init_word_app()

            self.update_status("正在读取Excel数据...")

            # 读取Excel数据
            df = pd.read_excel(self.excel_file)
            
            # 解析行号范围
            start_row = int(self.start_row.text())
            end_row = int(self.end_row.text())
            selected_rows = df.iloc[start_row-2:end_row-1]
            
            self.update_status("正在处理数据...")
            # 处理合并单元格：前向填充客户名称（假设客户名称在第3列）
            selected_rows.iloc[:, 2] = selected_rows.iloc[:, 2].ffill()
            
            # 按客户分组
            grouped = selected_rows.groupby(selected_rows.columns[2])

            # 预加载模板（性能优化：避免重复加载）
            template_path = self.template_path.text()
            self.update_status("正在加载Word模板...")
            template = self.load_template(template_path)

            # 预计算固定值（性能优化：避免重复计算）
            container_num_text = self.container_num.text()
            invoice_num_text = self.invoice_num.text()
            current_date = datetime.now()
            date_context = {
                'DD': current_date.strftime("%d"),
                'MM': current_date.strftime("%m"),
                'YYYY': current_date.strftime("%Y"),
            }

            # 处理输出目录
            transport_num = self.output_folder.text().strip()
            
            # 确保基础输出目录存在
            if not os.path.exists(self.default_output_dir):
                os.makedirs(self.default_output_dir)
            
            # 根据是否有运输编号决定使用哪个子文件夹
            if transport_num:
                output_dir = os.path.join(self.default_output_dir, transport_num)
            else:
                current_date = datetime.now().strftime("%Y%m%d")
                output_dir = os.path.join(self.default_output_dir, current_date)
            
            # 创建输出子文件夹
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            total_customers = len(grouped)
            current_customer = 0

            # 批量处理优化：分阶段处理
            # 第一阶段：生成所有Word文档
            word_files = []  # 存储生成的Word文件路径

            self.update_status("第一阶段：批量生成Word文档...")

            # 为每个客户生成Word文档
            for customer_name, group in grouped:
                current_customer += 1
                self.update_status(f"生成Word文档 {current_customer}/{total_customers}: {customer_name}")

                # 准备条目数据
                items = []
                total_original_amount = 0
                total_final_amount = 0
                # 折扣
                discount = 0

                for index, row in group.iterrows():
                    # 计算金额
                    volume = row.iloc[6]
                    unit_price = row.iloc[7]
                    quantity = row.iloc[5]

                    # 此处只能假设同一个客户采用相同的折扣，若情况不同，则需注意
                    if discount == 0:
                        discount = row.iloc[8] if pd.notna(row.iloc[8]) else 0

                    original = round(volume * unit_price)
                    total_original_amount += original
                    final = round(original * (1 - discount))
                    total_final_amount += final

                    # 优化：减少字符串格式化操作
                    items.append({
                        'container_num': container_num_text,  # 使用预计算值
                        'quantity': f"{quantity}件",
                        'volume': f"{volume}立方米",
                        'unit_price': f"{unit_price}比索",
                        'original_amount': f"{original}比索",
                        'discount': f"{int(discount*100)}%" if discount != 0 else "",
                        'final_amount': f"{final}比索"
                    })

                # 构建上下文（使用预计算值优化性能）
                context = {
                    'customer_name': customer_name,
                    'container_num': container_num_text,  # 使用预计算值
                    'invoice_num': invoice_num_text,      # 使用预计算值
                    'items': items,
                    'discount': f"{int(discount*100)}%" if discount != 0 else "",
                    'total_original_amount': f"{total_original_amount}比索",
                    'total_final_amount': f"{total_final_amount}\n比索",
                    **date_context  # 使用预计算的日期值
                }

                # 生成Word文件（使用真正的模板复用优化性能）
                doc = self.get_template()  # 重复使用同一个模板实例
                output_name = os.path.join(output_dir, f"{customer_name}_{container_num_text}.docx")
                doc.render(context)
                doc.save(output_name)

                # 记录生成的Word文件信息
                word_files.append({
                    'customer_name': customer_name,
                    'word_path': output_name,
                    'pdf_path': output_name.replace('.docx', '.pdf')
                })

            # 第二阶段：批量转换PDF
            self.update_status("第二阶段：批量转换PDF文档...")
            pdf_files = []  # 成功转换的PDF文件列表

            for i, file_info in enumerate(word_files):
                customer_name = file_info['customer_name']
                word_path = file_info['word_path']

                try:
                    self.update_status(f"转换PDF {i+1}/{len(word_files)}: {customer_name}")
                    pdf_output = self.convert_to_pdf(word_path)
                    pdf_files.append(pdf_output)

                except Exception as e:
                    self.error_log.append(f"{customer_name} PDF转换失败: {str(e)}")
                    self.update_status(f"PDF转换失败: {customer_name}")

            # 第三阶段：批量打开PDF文件
            if pdf_files:
                self.update_status("第三阶段：打开生成的PDF文件...")
                for pdf_file in pdf_files:
                    try:
                        if sys.platform == 'win32':
                            subprocess.Popen(['cmd', '/c', 'start', '', pdf_file], shell=True)
                        elif sys.platform == 'darwin':  # macOS
                            subprocess.Popen(['open', pdf_file])
                        else:  # linux
                            subprocess.Popen(['xdg-open', pdf_file])
                    except Exception as e:
                        print(f"无法打开PDF文件 {pdf_file}: {str(e)}")

            self.update_status("批量处理完成！")
            QMessageBox.information(self, "成功", f"发票生成完成！\n生成了 {len(word_files)} 个Word文档\n转换了 {len(pdf_files)} 个PDF文件")

        except Exception as e:
            self.update_status(f"遇到错误: {str(e)}")
        finally:
            # 清理Word应用实例（释放资源）
            self.cleanup_word_app()

            # 重新启用生成按钮并显示汇总信息
            self.generate_btn.setEnabled(True)
            if hasattr(self, 'error_log') and self.error_log:
                QMessageBox.warning(self, "处理结果", f"已完成处理，遇到{len(self.error_log)}个错误:\n" + "\n".join(self.error_log))
            else:
                self.status_label.setText("所有发票处理完成！")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = InvoiceGenerator()
    window.show()
    sys.exit(app.exec_())

# def start():
#     app = QApplication(sys.argv)
#     window = InvoiceGenerator()
#     window.show()
#     sys.exit(app.exec_())